import random
from venv import logger
from flask import Flask, render_template, make_response, request
from urllib.parse import urlparse, parse_qs

app = Flask(__name__)

import hashlib
import logging
from datetime import datetime
from search_xianmu_product import search_xianmu_product
from search_xianmu_directly_from_es import search_xianmu_product_directly_from_es
from category_prediction import (
    get_query_category_prediction,
    get_random_query,
    get_sku_category,
    top_20_query,
    middle_20_query,
    top_high_click_index_20_query,
    last_n_days,
)

from disperse_sku_by_its_spu import disperse_skus_v2, batch_disperse_skus

city_list=['杭州', '广州', '上海', '重庆', '成都', '青岛', '深圳', '武汉普冷','南京','苏州','长沙','厦门']


# 定义SPU提取函数
def spu_func(sku):
    return f"pd_id:{sku.get('pd_id')}"  # 提取pd_id


def stock_fun(sku):
    return sku["sufficient_stock"] if "sufficient_stock" in sku else True

# 仅仅展示一个结果集
@app.route("/search")
def search_without_arena():
    query = request.args.get("query", get_random_query()["query"])
    city = request.args.get("city", "杭州")
    page_size = int(request.args.get("page_size", default_page_size))

    results = search_xianmu_product(
        query, city, 200 + page_size
    )  # 获取600+page_size个，用以排序；

    # 为每个结果添加类目信息
    for result in results:
        result["category_name"] = get_sku_category(result["sku"])
        
    result_container = [
        {"version": "original_es", "results": results[0:page_size]},
    ]
    random.shuffle(result_container)

    return render_template(
        "search_arena_single.html",
        query=query,
        city=city,
        city_list=city_list,
        page_size=page_size,
        result_container=result_container,
        middle_20_query=middle_20_query,
        top_20_query=top_20_query,
        top_high_click_index_20_query=top_high_click_index_20_query,
        last_n_days=last_n_days,
        random_next_query=get_random_query(),
    )

@app.route("/")
def index():
    query = request.args.get("query", get_random_query()["query"])
    city = request.args.get("city", "杭州")
    page_size = int(request.args.get("page_size", default_page_size))
    if page_size > 200:
        logger.error(f"page_size:{page_size} is too large, set to 200")
        page_size = 200

    category_prediction = get_query_category_prediction(query=query)
    if "category" in category_prediction:
        logging.info(f"category_prediction found:{category_prediction}, query:{query}")

    results = search_xianmu_product(
        query, city, 200
    )  # 获取200个，用以排序；

    new_recall_results = search_xianmu_product_directly_from_es(
        query=query, city=city, size=200
    )

    # 为每个结果添加类目信息
    for result in results:
        result["category_name"] = get_sku_category(result["sku"])
    for result in new_recall_results:
        result["category_name"] = get_sku_category(result["sku"])

    original_top_result = results[0:page_size].copy()

    # 定义销量获取函数，获取SKU最近30天销量
    def sales_volume_func(sku):
        # 从SKU对象获取最近30天销量，若无则返回0
        return sku.get("monthly_gmv", 0.0)

    def category_func(sku):
        return sku["category"]

    # dispersed_sales_20：每批20个，先按库存再按销量倒序排序，并在销量排序时按类目分组组内倒序
    dispersed_sales_20 = batch_disperse_skus(
        new_recall_results.copy(),
        stock_fun=stock_fun,
        batch_size=20,
        sales_volume_func=sales_volume_func,
        category_func=category_func,  # 按类目分组，组内销量倒序
    )

    result_container = [
        {"version": "original_es", "results": original_top_result},
        {
            "version": "new_es_recall_no_rerank",
            "results": new_recall_results[0:page_size],
        },
        {
            "version": "new_es_recall_with_rerank", 
            "results": dispersed_sales_20[0:page_size],
        },
    ]
    random.shuffle(result_container)

    return render_template(
        "search_arena_es.html",
        query=query,
        city=city,
        city_list=city_list,
        page_size=page_size,
        result_container=result_container,
        middle_20_query=middle_20_query,
        top_20_query=top_20_query,
        top_high_click_index_20_query=top_high_click_index_20_query,
        last_n_days=last_n_days,
        random_next_query=get_random_query(),
    )


default_page_size = 20

from get_arena_db_connection_and_execute import get_arena_db_connection_and_execute


@app.route("/which-is-better", methods=["POST"])
def which_is_better():
    comment = ""
    if request.is_json:
        version = request.json.get("version", "")
        comment = request.json.get("comment", "")
    else:
        version = request.form.get("version", "")
        comment = request.form.get("comment", "")
    if version not in [
        "rewrote_query",
        "refine_items_with_category",
        "original_es",
        "both-are-poor",
        "they-are-tied",
    ]:
        return {"error": f"Invalid version:{version}"}, 400

    referer_url = request.referrer
    user_ip = request.remote_addr
    user_agent = request.user_agent.string
    userId = hashlib.sha256((user_ip + user_agent).encode()).hexdigest()

    logging.info(
        f"user_id:{userId},referer_url:{referer_url}, which-is-better:{version}"
    )

    query = ""
    city = ""
    page_size = default_page_size

    if referer_url:
        parsed_url = urlparse(referer_url)
        query_params = parse_qs(parsed_url.query)
        query = query_params.get("query", [""])[0]
        city = query_params.get("city", [""])[0]
        page_size = query_params.get("page_size", [f"{default_page_size}"])[0]
        try:
            page_size = int(page_size)
        except ValueError:
            logging.error(f"page_size:{page_size} is not a number")
            page_size = default_page_size

    get_arena_db_connection_and_execute(
        "INSERT INTO search_arena_record VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)",
        (
            userId,
            referer_url,
            user_ip,
            user_agent,
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            query,
            city,
            page_size,
            version,
            comment,
        ),
    )

    return {"message": "OK", "version": version}


def get_prefered_version_stats_v2():
    sql = """SELECT 
             CASE prefered_version
                WHEN 'rewrote_query' THEN 'query重写'
                WHEN 'refine_items_with_category' THEN '类目重排序'
                WHEN 'original_es' THEN '原始搜索'
                WHEN 'they-are-tied' THEN '都不错'
                WHEN 'both-are-poor' THEN '都很差'
             END as prefered_version,
             COUNT(*) as total_count,
             COUNT(DISTINCT userId) as unique_users,
             COUNT(DISTINCT query) as unique_queries
             FROM search_arena_record 
             WHERE prefered_version IN ('rewrote_query', 'refine_items_with_category', 'original_es', 'both-are-poor', 'they-are-tied')
             AND create_time >= '2024-12-11'
             GROUP BY prefered_version"""
    results = get_arena_db_connection_and_execute(sql)
    print(f"results:{results}")
    return results


@app.route("/report-v2")
def report_v2():
    stats = get_prefered_version_stats_v2()
    return render_template("report.html", stats=stats)


import argparse

if __name__ == "__main__":
    port = 5800
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--debug", action="store_true", default=False, help="Enable debug mode"
    )
    args, unknown = parser.parse_known_args()
    app.run(debug=args.debug, port=port, host="0.0.0.0")
