import requests
from requests.auth import HTTPBasicAuth
import json
import sys

sys.path.append("../")
from area_and_category_lib import get_area_no_by_name, get_sku_monthly_sales, get_category_text


def search_xianmu_product_directly_from_es(
    query,
    es_host="es-cn-i7m2pv3ht000o90dy.public.elasticsearch.aliyuncs.com:9200",
    es_index="summerfarm_item_info",
    brand_name=None,
    size=60,
    minimum_score=20.0,
    city="杭州",
):
    """
    直接从Elasticsearch搜索鲜沐商品。

    :param query: 搜索关键词。
    :param es_host: Elasticsearch主机地址，默认为公网地址。
    :param es_index: Elasticsearch索引名称，默认为summerfarm_item_info。
    :return: Elasticsearch搜索结果，JSON格式。
    """
    area_no = get_area_no_by_name(city=city)
    es_url = f"http://{es_host}/{es_index}/_search"
    auth = HTTPBasicAuth("elastic", "elastic@Xianmu0619")
    headers = {"Content-Type": "application/json"}
    must = [
        {"term": {"targetId": area_no}},
        {"terms": {"subType": [1, 2, 3, 4]}},  # 添加 subType 的 must 查询
    ]
    if brand_name is not None:
        must.append(
            {"term": {"brandName": brand_name}}
        )  # 这里应该是 brand_name 而不是 query
    search_body = {
        "from": 0,
        "size": size,
        "explain": True,
        "_source": [
            "title",
            "category",
            "brandName",
            "propertyValues",
            "keyProperty",
            "titlePure",
            "titleExt",
            "saleOut",
            "title.keyword",
            "price",
            "specification",
            "mainPicture",
            "itemCode",
            "marketItemId",
            "id",
            "categoryId",
            "storeQuantity",
        ],
        "query": {
            "function_score": {
                "query": {
                    "bool": {
                        "filter": [
                            {"term": {"targetId": area_no}},
                            {"term": {"onSale": 1}},
                            {"term": {"deleteFlag": 1}},
                            {"term": {"marketItemDeleteFlag": 1}},
                            {"term": {"show": 1}},
                            {"term": {"onSaleStrategyType": 3}},
                        ]
                    }
                },
                "functions": [
                    {
                        "filter": {
                            "bool": {
                                "must": [
                                    {"term": {"title": query}},
                                    {"term": {"category.name.keyword": query}},
                                ]
                            }
                        },
                        "weight": 120,
                    },
                    {
                        "filter": {
                            "bool": {
                                "must": {"term": {"title": query}},
                                "must_not": {"term": {"category.name.keyword": query}},
                            }
                        },
                        "weight": 80,
                    },
                    {
                        "filter": {
                            "bool": {
                                "should": [
                                    {"match": {"title": query}},
                                    {"term": {"category.name.keyword": query}},
                                ],
                                "minimum_should_match": 1,
                                "must_not": [{"term": {"title": query}}],
                            }
                        },
                        "weight": 40,
                    },
                    {
                        "filter": {
                            "multi_match": {
                                "query": query,
                                "fields": [
                                    "brandName",
                                    "propertyValues",
                                    "keyProperty",
                                    "titlePure",
                                    "titleExt",
                                ],
                                "analyzer": "xianmu_ik_max_word",
                            }
                        },
                        "field_value_factor": {
                            "field": "_matched",
                            "missing": 0,
                            "factor": 1,
                        },
                    },
                ],
                "score_mode": "sum",
                "boost_mode": "replace",
            }
        },
        "sort": [{"_score": {"order": "desc"}},{"storeQuantity": {"order": "desc"}}],
    }

    try:
        print(f"search_body:\n{json.dumps(search_body, indent=4, ensure_ascii=False)}")
        response = requests.post(
            es_url, auth=auth, headers=headers, data=json.dumps(search_body), timeout=5
        )
        response.raise_for_status()  # 检查请求是否成功
        hits = response.json().get("hits", {}).get("hits", [])
        # print(hits)
        result_list = []
        for hit in hits:
            source = hit.get("_source", {})
            result = {}
            sku = source.get("itemCode", "")
            result["monthly_sales"], result["monthly_gmv"] = get_sku_monthly_sales(sku)
            result["_score"] = hit.get("_score", "")
            result["sort_score"] = result["_score"]
            result["area_price"] = f"{city}, ¥{source['price']}"
            result["sku_id"] = f'{sku}, {source.get("specification", "")}'
            result["sku"] = sku
            result["spu_name"] = source.get("title", "")
            result["pd_id"] = source.get(
                "marketItemId", source.get("id", "")
            )  # 优先使用marketItemId, 否则使用id
            picture_path = source.get("mainPicture", "404.jpg")
            result["img_url"] = (
                picture_path
                if picture_path.startswith("http")
                else "https://azure.summerfarm.net/" + picture_path
            )+"?imageslim=3"
            result["brand"] = source.get("brandName", "")
            result["weight"] = source.get("specification", "")
            result["store_quantity"] = source.get("storeQuantity", 0)
            result["properties"] = source.get("keyProperty", [])
            result["property_values"] = source.get("propertyValues", [])
            result["category_id"] = source.get("categoryId", "")
            result["sufficient_stock"] = source.get("storeQuantity", 0) > 0
            result["category"] = get_category_text(sku_id=sku)
            category_list = source.get("category", [])
            result["front_category_name"] = category_list
            result_list.append(result)
        return result_list  # 将 hits 替换为转换后的 result_list
    except requests.exceptions.RequestException as e:
        print(f"Elasticsearch搜索请求失败: {e}")
        return []


# Example usage:
if __name__ == "__main__":
    query = "日清"
    results = search_xianmu_product_directly_from_es(query, brand_name="日清")
    if results:
        print(json.dumps(results, indent=2, ensure_ascii=False))
    else:
        print("搜索无结果或请求失败。")
