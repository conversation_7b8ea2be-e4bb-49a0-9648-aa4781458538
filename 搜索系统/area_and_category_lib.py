import sys
import os
import datetime
import threading
import sqlite3
import pandas as pd
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from odps_client import get_odps_sql_result_as_df

# --- 数据库设置 ---
# 使用用户主目录下的 sqlite 文件夹，符合 ~/sqlite 的要求
DB_DIR = Path.home() / "sqlite"
DB_DIR.mkdir(exist_ok=True)  # 确保目录存在
DB_PATH = DB_DIR / "search_system_cache.db"

# --- 全局变量 ---
area_df = None
category_df = None
sku_sales_volume_df = None
area_name_to_no_map = {}
data_lock = threading.Lock()  # 用于保护对全局DataFrame的并发访问
last_update_thread = None # 用于跟踪更新线程

def load_from_sqlite(table_name):
    """
    尝试从SQLite加载数据。
    如果表不存在、为空或数据超过24小时，则返回None。
    """
    if not DB_PATH.exists():
        return None
    
    with sqlite3.connect(DB_PATH) as conn:
        try:
            # 检查表是否存在
            cursor = conn.cursor()
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if cursor.fetchone() is None:
                return None  # 表不存在

            # 检查时间戳
            query = f"SELECT updated_at FROM {table_name} LIMIT 1"
            df_time = pd.read_sql(query, conn, parse_dates=['updated_at'])
            
            if df_time.empty:
                return None  # 表为空

            last_update = df_time['updated_at'].iloc[0].to_pydatetime()

            # 如果数据在24小时内，则加载并返回
            if datetime.datetime.now() - last_update < datetime.timedelta(hours=24):
                # print(f"从 SQLite 加载 {table_name}")
                return pd.read_sql(f"SELECT * FROM {table_name}", conn)
            else:
                # print(f"{table_name} 的 SQLite 数据已过时")
                return None
        except Exception as e:
            # 可能是表结构问题或数据库损坏
            print(f"从 SQLite 加载 {table_name} 时出错: {e}")
            return None

def save_to_sqlite(df, table_name):
    """将DataFrame保存到SQLite，并添加updated_at列"""
    if df is None:
        return
    df['updated_at'] = datetime.datetime.now()
    with sqlite3.connect(DB_PATH) as conn:
        df.to_sql(table_name, conn, if_exists='replace', index=False)
    print(f"已将 {table_name} 保存到 SQLite")

def fetch_and_save(table_name, fetch_func):
    """从ODPS获取数据并保存到SQLite"""
    print(f"从 ODPS 获取 {table_name}")
    try:
        df = fetch_func()
        save_to_sqlite(df, table_name)
        return df
    except Exception as e:
        print(f"从 ODPS 获取 {table_name} 失败: {e}")
        return None

# --- 数据获取函数 ---
def fetch_area_df():
    """从ODPS获取区域数据"""
    return get_odps_sql_result_as_df(
        """select area_no, area_name from summerfarm_tech.ods_area_df
        where ds=max_pt('summerfarm_tech.ods_area_df')"""
    )

def fetch_category_df():
    """从ODPS获取类目数据"""
    return get_odps_sql_result_as_df(
        f"""
    select CONCAT(category2,'_',category3,'_',category4) category,sku_id
    from summerfarm_tech.dim_sku_df
    where ds=max_pt('summerfarm_tech.dim_sku_df')
    group by category,sku_id
    """
    )

def fetch_sku_sales_volume_df():
    """从ODPS获取SKU销量数据"""
    one_month_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d")
    df = get_odps_sql_result_as_df(
        f"""
    SELECT  ds,sku
            ,SUM(amount) AS sales_volume
            ,round(SUM(actual_total_price),2) AS total_gmv
    FROM    summerfarm_tech.ods_order_item_df
    WHERE   ds = MAX_PT('summerfarm_tech.ods_order_item_df')
    AND     add_time >= '{one_month_ago} 00:00:00'
    GROUP BY sku,ds;
    """
    )
    df["total_gmv"] = df["total_gmv"].astype(float)
    df["sales_volume"] = df["sales_volume"].astype(int)
    df["sku"] = df["sku"].astype(str)
    return df

def _update_data_in_memory(new_area_df, new_category_df, new_sku_sales_df):
    """安全地更新内存中的全局变量"""
    global area_df, category_df, sku_sales_volume_df, area_name_to_no_map
    with data_lock:
        if new_area_df is not None:
            area_df = new_area_df
            # 更新 area_name_to_no_map
            area_name_to_no_map.clear()
            for _, row in area_df.iterrows():
                area_name_to_no_map[row["area_name"]] = row["area_no"]
        
        if new_category_df is not None:
            category_df = new_category_df
        
        if new_sku_sales_df is not None:
            sku_sales_volume_df = new_sku_sales_df

def _reload_all_data():
    """
    重新加载所有数据。首先从ODPS获取，然后更新SQLite和内存。
    这是一个阻塞操作。
    """
    print("开始重新加载所有数据...")
    new_area_df = fetch_and_save('area_df', fetch_area_df)
    new_category_df = fetch_and_save('category_df', fetch_category_df)
    new_sku_sales_df = fetch_and_save('sku_sales_volume_df', fetch_sku_sales_volume_df)
    
    _update_data_in_memory(new_area_df, new_category_df, new_sku_sales_df)
    print("所有数据重新加载完成。")

def _initial_load():
    """
    模块加载时的初始化函数。
    会尝试从SQLite加载，如果失败或数据过时，则从ODPS加载。
    """
    print("模块初始化，开始加载数据...")
    # 尝试从SQLite加载
    with data_lock:
        global area_df, category_df, sku_sales_volume_df
        area_df = load_from_sqlite('area_df')
        category_df = load_from_sqlite('category_df')
        sku_sales_volume_df = load_from_sqlite('sku_sales_volume_df')

    # 检查哪些数据加载失败，并从ODPS重新获取
    should_reload = False
    if area_df is None or category_df is None or sku_sales_volume_df is None:
        print("部分或全部本地数据无效，将从ODPS重新加载。")
        should_reload = True

    if should_reload:
        _reload_all_data()
    else:
        # 如果从SQLite加载成功，也需要更新内存中的map
        _update_data_in_memory(area_df, category_df, sku_sales_volume_df)
        print("从 SQLite 加载所有数据成功。")

# --- 首次加载 ---
_initial_load()

def _check_and_reload_async():
    """
    检查数据是否需要异步更新。
    这是一个非阻塞操作，会启动一个新线程执行更新。
    """
    global last_update_thread
    # 如果上一个更新线程还在运行，则不启动新的
    if last_update_thread and last_update_thread.is_alive():
        print("更新线程已在运行中。")
        return

    # 轻量级检查，只检查一个表的时间戳
    df = load_from_sqlite('area_df')
    if df is not None:
        # 数据是新鲜的，不需要更新
        return
    
    print("数据已过时或不存在，启动异步更新线程。")
    last_update_thread = threading.Thread(target=_reload_all_data)
    last_update_thread.start()

# --- 公共接口 ---
def get_category_text(sku_id):
    """获取SKU的类目信息"""
    _check_and_reload_async()  # 每次调用时检查是否需要更新
    with data_lock:
        if category_df is None:
            return "类目数据正在加载中"
        
        _df = category_df[category_df["sku_id"] == sku_id]
        if _df.empty:
            return "没有找到类目"
        else:
            return _df.iloc[0]["category"]

def get_area_no_by_name(city: str, default_value: str = "1001"):
    """通过城市名获取区域编号"""
    _check_and_reload_async()
    with data_lock:
        if city is None or city not in area_name_to_no_map:
            return default_value
        return area_name_to_no_map[city]

def get_sku_monthly_sales(sku: str) -> tuple[int, float]:
    """获取SKU的月销量"""
    _check_and_reload_async()
    with data_lock:
        if sku_sales_volume_df is None:
            return (0, 0.0)
        
        _df = sku_sales_volume_df[sku_sales_volume_df["sku"] == str(sku)]
        if _df.empty:
            return (0, 0.0)
        else:
            # 一个SKU可能有多条记录（如果按ds分组），这里应该聚合
            sales_volume = _df["sales_volume"].sum()
            total_gmv = _df["total_gmv"].sum()
            return (int(sales_volume), float(total_gmv))
